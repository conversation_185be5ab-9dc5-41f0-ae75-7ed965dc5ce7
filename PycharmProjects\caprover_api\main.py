import json
import os

import requests
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
from caprover_api import caprover_api
from functools import wraps
from datetime import timedelta
import logging
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

# Initialize Flask app
app = Flask(__name__)

# Load env variables
load_dotenv()

# Configuration
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', ''),
    SESSION_COOKIE_SECURE=False,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)
)

# Configure logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/application.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('Application startup')

# CapRover API configuration
APP_NAME = os.environ.get('APP_NAME', '')
CAPROVER_URL = os.environ.get('CAPROVER_URL', '')
CAPROVER_PASSWORD = os.environ.get('CAPROVER_PASSWORD', '')

# Initialize the Caprover API client
cap = caprover_api.CaproverAPI(
    dashboard_url=CAPROVER_URL,
    password=CAPROVER_PASSWORD
)

# Admin credentials (should be stored securely in environment variables)
ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '')
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', '')


# Simple login_required decorator
def login_required(f):
    @wraps(f)
    def wrapped(*args, **kwargs):
        if not session.get("logged_in"):
            flash("Please login first", "error")
            return redirect(url_for("login"))
        return f(*args, **kwargs)

    return wrapped


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")

        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session["logged_in"] = True
            session.permanent = True
            app.logger.info(f"User {username} logged in successfully")
            return redirect(url_for("dashboard"))
        else:
            app.logger.warning(f"Failed login attempt for user {username}")
            flash("Invalid credentials", "error")
    return render_template("login.html", app_name=APP_NAME)


@app.route("/logout")
@login_required
def logout():
    session.pop("logged_in", None)
    flash("Logged out successfully", "success")
    return redirect(url_for("login"))


@app.route("/")
@login_required
def dashboard():
    return render_template("dashboard.html", app_name=APP_NAME)


def clean_docker_logs(raw_logs):
    """
    Clean Docker log format and ANSI escape sequences from logs.
    Docker logs come with headers like: \x01\x00\x00\x00\x00\x00\x00[LENGTH]
    followed by ANSI escape sequences.
    """
    if not raw_logs:
        return ""

    import re
    lines = raw_logs.split('\n')
    cleaned_lines = []

    for i, line in enumerate(lines):
        if not line:
            continue

        # Remove Docker log format headers (8 bytes: stream type + 7 bytes length)
        # Docker format: [stream_type][0x00][0x00][0x00][length_bytes][log_data]
        if len(line) >= 8 and line[0] in ['\x01', '\x02']:  # stdout or stderr
            # Skip the 8-byte Docker header
            line = line[8:]

        # Remove ANSI escape sequences completely
        # This regex removes ESC[...m (colors), ESC[...J (clear), ESC[...K (erase), etc.
        line = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', line)

        # Remove other control characters except newlines and tabs
        line = re.sub(r'[\x00-\x08\x0B-\x1F\x7F-\x9F]', '', line)

        # Clean up any remaining artifacts and extra whitespace
        line = line.strip()

        # Check if this line has a timestamp (new log entry)
        has_timestamp = re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', line)

        if has_timestamp:
            # Remove various ANSI artifact patterns from timestamped lines:
            # 1. Single letter followed by space: "p ", "b ", "c "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\s+', r'\1', line)
            # 2. Letter followed by numbers and space: "b0 ", "p8 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\d+\s+', r'\1', line)
            # 3. Letter followed by quote and numbers: "p'9 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\'\d+\s+', r'\1', line)
            # 4. Letter combinations like "po8 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]{1,2}\d*\s+', r'\1', line)
            # 5. Handle standalone single letters at the end of timestamp line
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]$', r'\1', line)

            # If after cleaning the timestamp line is empty or just whitespace,
            # check if the next line should be merged with this timestamp
            line_after_timestamp = re.sub(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', '', line).strip()
            if not line_after_timestamp and i + 1 < len(lines):
                # Look at the next line to see if it's a continuation
                next_line = lines[i + 1]
                # Remove Docker headers from next line
                if len(next_line) >= 8 and next_line[0] in ['\x01', '\x02']:
                    next_line = next_line[8:]
                # Clean ANSI from next line
                next_line = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', next_line)
                next_line = re.sub(r'[\x00-\x08\x0B-\x1F\x7F-\x9F]', '', next_line)
                next_line = next_line.strip()

                # If next line doesn't have timestamp, merge it
                if next_line and not re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', next_line):
                    timestamp_part = re.match(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)', line).group(1)
                    line = timestamp_part + next_line
                    # Skip the next line since we merged it
                    lines[i + 1] = ""

        if line:
            cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)


@app.route("/api/logs", methods=["GET"])
@login_required
def api_logs():
    try:
        response = cap.session.get(
            cap._build_url(f"/api/v2/user/apps/appData/{APP_NAME}/logs"),
            headers=cap.headers
        )
        logs_data = cap._check_errors(response.json()).get("data")

        # Clean the logs if they exist
        if isinstance(logs_data, dict) and 'logs' in logs_data:
            raw_logs = logs_data['logs']
            cleaned_logs = clean_docker_logs(raw_logs)
            logs_data['logs'] = cleaned_logs
            app.logger.info(f"Cleaned logs: {len(cleaned_logs)} characters from {len(raw_logs)} raw characters")

        return jsonify(logs_data)
    except Exception as e:
        app.logger.error(f"Error fetching logs: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/logs/debug", methods=["GET"])
@login_required
def api_logs_debug():
    """Debug endpoint to see raw vs cleaned logs"""
    try:
        response = cap.session.get(
            cap._build_url(f"/api/v2/user/apps/appData/{APP_NAME}/logs"),
            headers=cap.headers
        )
        logs_data = cap._check_errors(response.json()).get("data")

        if isinstance(logs_data, dict) and 'logs' in logs_data:
            raw_logs = logs_data['logs']
            cleaned_logs = clean_docker_logs(raw_logs)

            # Test emoji addition (simulate what frontend should do)
            test_lines = [
                "2025-07-27T03:19:51.457975454Z Session management will initialize after database connection...",
                "2025-07-27T03:19:54.541928483Z Server starting - initializing session management...",
                "2025-07-27T03:19:54.552857907Z Found 0 existing sessions in database",
                "2025-07-27T03:19:54.556592248Z Hard deleted 0 sessions from database",
                "2025-07-27T03:19:54.556635949Z Clearing any in-memory sessions...",
                "2025-07-27T03:19:54.556640107Z Session management initialized successfully - database is clean"
            ]

            # Return both raw and cleaned for comparison
            return jsonify({
                "raw_logs": raw_logs[:1000],  # First 1000 chars
                "cleaned_logs": cleaned_logs[:1000],  # First 1000 chars
                "raw_length": len(raw_logs),
                "cleaned_length": len(cleaned_logs),
                "test_emoji_lines": test_lines,
                "sample_cleaned_lines": cleaned_logs.split('\n')[:10]
            })

        return jsonify({"error": "No logs found"})
    except Exception as e:
        app.logger.error(f"Error fetching debug logs: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/logs/test", methods=["GET"])
@login_required
def api_logs_test():
    """Test endpoint with sample logs that should have emojis"""
    sample_logs = """2025-07-27T03:19:51.450622497Z Background cleanup task started (interval: 10min)
2025-07-27T03:19:51.450764541Z Token expiration monitoring started
2025-07-27T03:19:51.453004187Z SocketManager initialized with config: {"sessionTimeout": 60, "cleanupInterval": 10}
2025-07-27T03:19:51.457975454Z Session management will initialize after database connection...
2025-07-27T03:19:54.541424428Z Database connected successfully
2025-07-27T03:19:54.541928483Z Server starting - initializing session management...
2025-07-27T03:19:54.552857907Z Found 0 existing sessions in database
2025-07-27T03:19:54.556592248Z Hard deleted 0 sessions from database
2025-07-27T03:19:54.556635949Z Clearing any in-memory sessions...
2025-07-27T03:19:54.556640107Z Session management initialized successfully - database is clean
2025-07-27T03:19:54.556643834Z Session management initialized - all existing sessions cleared
2025-07-27T03:19:54.556675632Z Scheduled session cleanup every 30 minutes"""

    return jsonify({"logs": sample_logs})


@app.route("/api/env", methods=["GET", "POST"])
@login_required
def api_env():
    if request.method == "GET":
        try:
            # Retrieve app configuration and extract environment variables.
            app_data = cap.get_app(APP_NAME)
            env_vars = app_data.get("envVars", {})
            return jsonify({"success": True, "env": env_vars})
        except Exception as e:
            app.logger.error(f"Error fetching env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    else:
        data = request.get_json()
        if not data or "env" not in data:
            return jsonify({"success": False, "error": "Invalid request data"}), 400

        arr = data.get("env")
        try:
            app_data = cap.get_app(APP_NAME)
            app_data["envVars"] = arr

            response = cap.session.post(
                cap._build_url("/api/v2/user/apps/appDefinitions/update"),
                headers=cap.headers, data=json.dumps(app_data)
            )
            result = cap._check_errors(response.json())
            app.logger.info(f"Environment variables updated: {result}")
            return jsonify({"success": True})
        except Exception as e:
            app.logger.error(f"Error updating env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/restart", methods=["POST"])
@login_required
def api_restart():
    try:
        result = cap.update_app(APP_NAME, instance_count=1)
        app.logger.info(f"App restart initiated: {result}")
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error restarting app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/rebuild", methods=["POST"])
@login_required
def api_rebuild():
    try:
        url = os.environ.get('REBUILD_URL', '')
        if url == '':
            return jsonify({"success": False, "error": "this app doesn't support rebuild!"}), 500
        result = requests.post(url)
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error rebuilding app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f'Server Error: {str(error)}')
    return render_template('500.html'), 500


if __name__ == "__main__":
    # In production, use a proper WSGI server like Gunicorn
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
