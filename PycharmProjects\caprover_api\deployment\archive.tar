app.py                                                                                              000666  000000  000000  00000014160 15001410762 010360  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         import json
import os

import requests
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
from caprover_api import caprover_api
from functools import wraps
from datetime import timedelta
import logging
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

# Initialize Flask app
app = Flask(__name__)

# Load env variables
load_dotenv()

# Configuration
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', ''),
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)
)

# Configure logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/application.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('Application startup')

# CapRover API configuration
APP_NAME = os.environ.get('APP_NAME', '')
CAPROVER_URL = os.environ.get('CAPROVER_URL', '')
CAPROVER_PASSWORD = os.environ.get('CAPROVER_PASSWORD', '')

# Initialize the Caprover API client
cap = caprover_api.CaproverAPI(
    dashboard_url=CAPROVER_URL,
    password=CAPROVER_PASSWORD
)

# Admin credentials (should be stored securely in environment variables)
ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '')
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', '')


# Simple login_required decorator
def login_required(f):
    @wraps(f)
    def wrapped(*args, **kwargs):
        if not session.get("logged_in"):
            flash("Please login first", "error")
            return redirect(url_for("login"))
        return f(*args, **kwargs)

    return wrapped


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")

        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session["logged_in"] = True
            session.permanent = True
            app.logger.info(f"User {username} logged in successfully")
            return redirect(url_for("dashboard"))
        else:
            app.logger.warning(f"Failed login attempt for user {username}")
            flash("Invalid credentials", "error")
    return render_template("login.html", app_name=APP_NAME)


@app.route("/logout")
@login_required
def logout():
    session.pop("logged_in", None)
    flash("Logged out successfully", "success")
    return redirect(url_for("login"))


@app.route("/")
@login_required
def dashboard():
    return render_template("dashboard.html", app_name=APP_NAME)


@app.route("/api/logs", methods=["GET"])
@login_required
def api_logs():
    try:
        response = cap.session.get(
            cap._build_url(f"/api/v2/user/apps/appData/{APP_NAME}/logs"),
            headers=cap.headers
        )
        logs = cap._check_errors(response.json()).get("data")
        return jsonify(logs)
    except Exception as e:
        app.logger.error(f"Error fetching logs: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/env", methods=["GET", "POST"])
@login_required
def api_env():
    if request.method == "GET":
        try:
            # Retrieve app configuration and extract environment variables.
            app_data = cap.get_app(APP_NAME)
            env_vars = app_data.get("envVars", {})
            return jsonify({"success": True, "env": env_vars})
        except Exception as e:
            app.logger.error(f"Error fetching env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    else:
        data = request.get_json()
        if not data or "env" not in data:
            return jsonify({"success": False, "error": "Invalid request data"}), 400

        arr = data.get("env")
        try:
            app_data = cap.get_app(APP_NAME)
            app_data["envVars"] = arr

            response = cap.session.post(
                cap._build_url("/api/v2/user/apps/appDefinitions/update"),
                headers=cap.headers, data=json.dumps(app_data)
            )
            result = cap._check_errors(response.json())
            app.logger.info(f"Environment variables updated: {result}")
            return jsonify({"success": True})
        except Exception as e:
            app.logger.error(f"Error updating env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/restart", methods=["POST"])
@login_required
def api_restart():
    try:
        result = cap.update_app(APP_NAME, instance_count=1)
        app.logger.info(f"App restart initiated: {result}")
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error restarting app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/rebuild", methods=["POST"])
@login_required
def api_rebuild():
    try:
        url = os.environ.get('REBUILD_URL', '')
        if url == '':
            return jsonify({"success": False, "error": "this app doesn't support rebuild!"}), 500
        result = requests.post(url)
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error rebuilding app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f'Server Error: {str(error)}')
    return render_template('500.html'), 500


if __name__ == "__main__":
    # In production, use a proper WSGI server like Gunicorn
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
                                                                                                                                                                                                                                                                                                                                                                                                                Dockerfile                                                                                          000666  000000  000000  00000000460 14765604542 011237  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

# Directly run the Gunicorn command
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--access-logfile", "-", "--error-logfile", "-", "app:app"]                                                                                                                                                                                                                requirements.txt                                                                                    000666  000000  000000  00000000102 14765611502 012514  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         flask==2.3.3
gunicorn==21.2.0
caprover-api
python-dotenv==1.0.0                                                                                                                                                                                                                                                                                                                                                                                                                                                              templates/                                                                                          000777  000000  000000  00000000000 15001411075 011220  5                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         templates/404.html                                                                                  000666  000000  000000  00000001113 14765573001 012427  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         <!DOCTYPE html>
<html>
<head>
    <link rel="icon" type="image/svg+xml" href="https://fonts.gstatic.com/s/i/materialiconsoutlined/error/v14/24px.svg">
    <title>404 - Page Not Found</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        h1 { color: #444; }
        a { color: #0066cc; }
    </style>
</head>
<body>
    <h1>404 - Page Not Found</h1>
    <p>The page you're looking for doesn't exist.</p>
    <p><a href="/">Return to dashboard</a></p>
</body>
</html>                                                                                                                                                                                                                                                                                                                                                                                                                                                     templates/500.html                                                                                  000666  000000  000000  00000001125 14765573004 012432  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         <!DOCTYPE html>
<html>
<head>
    <link rel="icon" type="image/svg+xml" href="https://fonts.gstatic.com/s/i/materialiconsoutlined/error/v14/24px.svg">
    <title>500 - Server Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        h1 { color: #444; }
        a { color: #0066cc; }
    </style>
</head>
<body>
    <h1>500 - Server Error</h1>
    <p>Something went wrong on our end. Please try again later.</p>
    <p><a href="/">Return to dashboard</a></p>
</body>
</html>                                                                                                                                                                                                                                                                                                                                                                                                                                           templates/dashboard.html                                                                            000666  000000  000000  00000121250 14771527121 014053  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="icon" type="image/svg+xml" href="https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/terminal.svg">
    <title>{{ app_name }}</title>
    <!-- Lucide icons -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js"></script>
    <style>
        :root {
            /* Updated Color Palette */
            --primary-color: #58a6ff; /* Brighter Blue */
            --primary-hover-color: #388bfd;
            --secondary-color: #161b22; /* Deeper Gray */
            --card-bg: #1f242c; /* Slightly lighter card bg */
            --border-color: #30363d;
            --text-color: #c9d1d9; /* Lighter text */
            --text-muted-color: #8b949e;
            --success-color: #3fb950;
            --success-hover-color: #2ea043;
            --danger-color: #f85149;
            --danger-hover-color: #da3633;
            --bg-gradient-from: #0d1117; /* Darker base */
            --bg-gradient-to: #161b22;
            --input-bg: #0d1117;
            --input-focus-border: var(--primary-color);
            --code-bg: #010409; /* Very dark for code */

            /* Sizing & Transitions */
            --border-radius-sm: 0.3rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --transition-duration: 0.2s;

            /* Helper for focus shadow - Calculate RGB from hex */
            --rgb-primary: 88, 166, 255; /* Calculated from #58a6ff */
        }

        /* Reset & Base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
            background: linear-gradient(145deg, var(--bg-gradient-from), var(--bg-gradient-to));
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header */
        header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .header-content {
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1.5rem; /* Increased padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem; /* Consistent gap */
        }

        .logo-section i {
            color: var(--primary-color); /* Use primary color */
            width: 1.75rem; /* Slightly larger */
            height: 1.75rem;
            flex-shrink: 0;
        }

        .logo-section h1 {
            font-size: 1.5rem; /* Adjusted size */
            font-weight: 600; /* Slightly less bold */
            background: linear-gradient(to right, #a1c4fd, var(--primary-color)); /* Subtle gradient */
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            white-space: nowrap;
        }

        .header-actions {
            display: flex;
            gap: 0.75rem; /* Consistent gap */
            flex-wrap: wrap;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center; /* Center content */
            gap: 0.5rem;
            padding: 0.6rem 1.2rem; /* Adjusted padding */
            border: 1px solid transparent; /* Base border */
            border-radius: var(--border-radius-md);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1; /* Prevent text jump */
            transition: background-color var(--transition-duration) ease, border-color var(--transition-duration) ease, opacity var(--transition-duration) ease, transform var(--transition-duration) ease;
            color: #ffffff;
            text-decoration: none; /* For link button */
        }

        .btn i {
            width: 1rem;
            height: 1rem;
            /* margin-right: 0.1rem; */ /* Removed for better centering with loader */
        }
         /* Specific style for icon + text */
         .btn i:not(:last-child) {
             margin-right: 0.3rem;
         }
         /* Specific style for loader */
         .btn .spin {
             margin-right: 0.4rem;
         }

        .btn:hover {
            filter: brightness(1.1); /* General hover effect */
        }

        .btn:active {
            transform: scale(0.98); /* Click effect */
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: #ffffff; /* Ensure contrast */
        }
        .btn-primary:hover {
            background-color: var(--primary-hover-color);
            border-color: var(--primary-hover-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        .btn-success:hover {
            background-color: var(--success-hover-color);
            border-color: var(--success-hover-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        .btn-danger:hover {
            background-color: var(--danger-hover-color);
            border-color: var(--danger-hover-color);
        }

        /* Icon-only button variant (used for refresh logs) */
        .btn-icon {
            padding: 0.5rem;
            background-color: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-muted-color);
        }
        .btn-icon:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            border-color: var(--text-muted-color);
            filter: none; /* Override general hover */
        }
        .btn-icon i {
            margin-right: 0; /* No extra margin for icon-only */
        }


        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            filter: grayscale(50%);
            transform: none; /* Disable active transform */
        }

        /* Navigation */
        nav {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .nav-content {
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1.5rem; /* Match header padding */
            display: flex;
            gap: 0.5rem; /* Reduced gap for tabs */
            flex-wrap: wrap;
            overflow-x: auto; /* Allow horizontal scroll on small screens */
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem; /* Adjusted padding */
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-muted-color);
            background: none;
            border: none;
            border-bottom: 2px solid transparent; /* Placeholder for alignment */
            cursor: pointer;
            position: relative;
            transition: color var(--transition-duration) ease, background-color var(--transition-duration) ease, border-color var(--transition-duration) ease;
            border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0; /* Top rounding */
            margin-bottom: -1px; /* Overlap border-bottom */
            white-space: nowrap; /* Prevent tab text wrapping */
        }

        .nav-btn:hover {
            color: var(--text-color);
            background-color: rgba(255, 255, 255, 0.03); /* Subtle hover */
        }

        .nav-btn.active {
            color: var(--primary-color);
            font-weight: 600;
            border-bottom-color: var(--primary-color); /* Active line indicator */
            background-color: var(--card-bg); /* Blend with card bg */
        }

        .nav-btn i {
             width: 1rem;
             height: 1rem;
        }

        /* Main Container */
        .container {
            max-width: 80rem;
            margin: 2.5rem auto; /* Increased top/bottom margin */
            padding: 0 1.5rem;
        }

        /* Card Styling */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin-bottom: 2.5rem; /* Increased spacing */
        }

        .card-header, .card-footer {
            padding: 1rem 1.5rem; /* Increased padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.1); /* Subtle distinction */
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap; /* Allow wrapping */
            gap: 0.75rem; /* Gap between items if they wrap */
        }

        .card-header h2 {
            font-size: 1.125rem; /* Slightly smaller */
            font-weight: 600;
            color: var(--text-color); /* Brighter header text */
            margin-right: auto; /* Push other items to the right */
        }

        .card-footer {
            border-top: 1px solid var(--border-color);
            border-bottom: none;
            background-color: transparent; /* Footer matches card body */
             /* Allow buttons to wrap is inherited */
             /* gap: 0.75rem; is inherited */
        }

        .card-body {
            padding: 1.5rem; /* Padding for content inside cards without specific padding */
        }

        /* Logs & Preformatted Text */
        pre {
            padding: 1.5rem;
            background: var(--code-bg); /* Darker background for contrast */
            color: #e6edf3; /* Lighter code text */
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.875rem;
            overflow: auto; /* Use auto for scrollbars only when needed */
            max-height: 600px;
            border-radius: var(--border-radius-md); /* Round corners */
            white-space: pre-wrap; /* Wrap long lines */
            word-break: break-all; /* Break long words/strings */
        }
         /* Apply rounding if pre is direct child of card-body */
        .card-body > pre {
           margin: 0; /* Remove default pre margin if inside card-body */
           border-radius: var(--border-radius-md);
        }


        /* === Environment Variables Grid - UPDATED RULE === */
        .env-grid {
            display: grid;
            grid-template-columns: 1fr 1fr; /* Two columns for key/value */
            gap: 1rem;               /* Gap between key & value, and between rows */
            padding: 1.5rem;
            align-items: start;      /* <<< ADDED THIS LINE: Align items to the start (top) */
                                     /* Prevents vertical stretching, fixing height difference */
        }

        /* Removed the specific margin rule for even children as the main gap handles spacing better */
        /* Add specific margin to separate pairs */
        /* .env-grid .input-field:nth-child(even) { margin-bottom: 1.25rem; } */

        /* Removed the last-child rule as it's no longer needed without the nth-child rule */
        /* Remove bottom margin from the very last input */
        /* .env-grid .input-field:last-child { margin-bottom: 0; } */
        /* === Environment Variables Grid - END OF CHANGES === */


        .input-field {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background: var(--input-bg);
            color: var(--text-color);
            font-size: 0.9rem;
            transition: border-color var(--transition-duration) ease, box-shadow var(--transition-duration) ease;
            outline: none;
            /* No explicit height needed - let padding/font determine it */
        }

        .input-field::placeholder {
            color: var(--text-muted-color);
            opacity: 0.7;
        }

        .input-field:focus {
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 3px rgba(var(--rgb-primary), 0.3); /* Use RGB for alpha */
        }


        /* Bulk Edit */
        .bulk-edit-toggle {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toggle-label {
            font-size: 0.875rem;
            color: var(--text-muted-color);
            white-space: nowrap; /* Prevent wrapping */
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 3rem; /* Larger */
            height: 1.75rem; /* Larger */
            flex-shrink: 0; /* Prevent shrinking */
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #30363d; /* Darker inactive */
            transition: var(--transition-duration) ease;
            border-radius: 1.75rem; /* Fully rounded */
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 1.25rem; /* Larger knob */
            width: 1.25rem;
            left: 0.25rem;
            bottom: 0.25rem;
            background-color: #ffffff;
            transition: var(--transition-duration) ease;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(1.25rem); /* Adjusted travel distance */
        }

        .bulk-textarea {
            /* Applied within the envSection, not inside card-body */
            display: block; /* Ensure it takes block layout */
            width: calc(100% - 3rem); /* Full width minus padding */
            margin: 1.5rem; /* Match card-body/env-grid padding */
            min-height: 20rem; /* Use min-height */
            background: var(--code-bg); /* Match pre background */
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: 1rem;
            color: var(--text-color);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.9rem;
            /* margin: 1.5rem 0 0; Add top margin */
            resize: vertical;
            outline: none;
            transition: border-color var(--transition-duration) ease, box-shadow var(--transition-duration) ease;
        }

        .bulk-textarea::placeholder {
            color: var(--text-muted-color);
            opacity: 0.7;
        }

        .bulk-textarea:focus {
             border-color: var(--input-focus-border);
            box-shadow: 0 0 0 3px rgba(var(--rgb-primary), 0.3);
        }

        /* Notifications */
        .notification {
            position: fixed;
            bottom: 1.5rem; /* Position at bottom */
            right: 1.5rem;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius-md);
            color: #ffffff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.4s ease-out forwards; /* Add forwards */
            z-index: 50;
            font-size: 0.9rem;
            font-weight: 500;
            max-width: calc(100% - 3rem); /* Prevent overflow on small screens */
        }

        .notification.success {
            background-color: var(--success-color);
        }

        .notification.error {
            background-color: var(--danger-color);
        }

        @keyframes slideUp { /* Changed animation */
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        /* Add animation for slideOut if needed by JS */
         @keyframes slideDown {
             from {
                 transform: translateY(0);
                 opacity: 1;
             }
             to {
                 transform: translateY(100%);
                 opacity: 0;
             }
         }


        /* Custom Scrollbars (Webkit) */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: var(--secondary-color);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted-color);
        }


        /* Responsive */
        @media (max-width: 768px) {
            body { font-size: 15px; } /* Slightly adjust base size */

            .header-content {
                flex-direction: column;
                align-items: flex-start; /* Align items start */
                gap: 1.5rem; /* Increase gap when stacked */
                padding: 0 1rem;
            }

            .header-actions {
                width: 100%; /* Make actions full width */
                justify-content: center; /* Center buttons */
                gap: 0.5rem;
            }
             .header-actions .btn {
                 flex-grow: 1; /* Allow buttons to grow */
                 font-size: 0.85rem; /* Smaller text */
                 padding: 0.6rem 0.8rem;
             }

            .nav-content {
                 padding: 0 1rem; /* Slightly less padding */
            }
            .nav-btn {
                 padding: 0.75rem 0.8rem;
                 font-size: 0.85rem;
            }

             .container {
                 padding: 0 1rem;
                 margin: 1.5rem auto;
             }

            .card-header, .card-footer {
                padding: 1rem; /* Reduce padding on small screens */
                 /* Keep flex-direction row unless absolutely necessary */
                 /* align-items: center; */ /* Re-center items if they wrap */
            }
            .card-header h2 {
                font-size: 1.05rem;
            }
             /* Ensure toggle is aligned in stacked header */
            .card-header .bulk-edit-toggle {
                 margin-left: auto; /* Push toggle to the right if wraps */
                 /* margin-top: 0.5rem; */
                 /* width: 100%; */
                 /* justify-content: flex-end; */
            }


            .card-body { padding: 1rem; }
            pre { padding: 1rem; font-size: 0.8rem; }

            /* === ENV Grid Responsive - START === */
            .env-grid {
                grid-template-columns: 1fr; /* Single column on mobile */
                gap: 0.5rem; /* Smaller gap between key and value */
                padding: 1rem;
                /* align-items: start; is inherited and has no effect in single column */
            }

            /* Removed nth-child margin rule */
            /* .env-grid .input-field:nth-child(even) { margin-bottom: 1rem; } */
            /* === ENV Grid Responsive - END === */


            .input-field { font-size: 0.875rem; }

            .bulk-textarea {
                margin: 1rem;
                width: calc(100% - 2rem);
            }

            .card-footer .btn {
                width: 100%; /* Make footer buttons full width */
                /* font-size: 0.9rem; */ /* Optional: slightly larger font on mobile buttons */
            }
             .card-footer .btn:not(:first-child) {
                 /* margin-top: 0.5rem; */ /* Gap handles spacing */
             }

             .notification {
                 right: 1rem;
                 bottom: 1rem;
                 left: 1rem; /* Make notification wider on mobile */
                 text-align: center;
                 padding: 0.8rem 1rem;
                 font-size: 0.85rem;
                 max-width: calc(100% - 2rem);
             }
        }

         /* Loader animation */
         .spin { animation: spin 1s linear infinite; display: inline-block; }
         @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }

    </style>
</head>
<body>
<header>
    <div class="header-content">
        <div class="logo-section">
            <i data-lucide="terminal"></i>
            <h1>{{ app_name }}</h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" id="restartBtn">
                <i data-lucide="rotate-cw"></i>
                Restart
            </button>
            <button class="btn btn-success" id="rebuildBtn">
                <i data-lucide="refresh-cw"></i>
                Rebuild
            </button>
            <a href="/logout" class="btn btn-danger">
                <i data-lucide="log-out"></i>
                Logout
            </a>
        </div>
    </div>
</header>

<nav>
    <div class="nav-content">
        <button id="logsTab" class="nav-btn active">
            <i data-lucide="align-left"></i> <!-- Changed icon for variety -->
            Deployment
        </button>
        <button id="envTab" class="nav-btn">
            <i data-lucide="settings-2"></i> <!-- Changed icon for variety -->
            App Configs
        </button>
    </div>
</nav>

<main class="container">
    <!-- Logs Card -->
    <section id="logsSection" class="card">
        <div class="card-header">
            <h2>Application Logs</h2>
            <!-- Using icon-only button style -->
            <button class="btn btn-icon" id="refreshLogsBtn" title="Refresh Logs">
                <i data-lucide="refresh-cw"></i>
            </button>
        </div>
         <div class="card-body">
             <pre id="logsContent">Loading logs...</pre>
         </div>
    </section>

    <!-- Environment Variables Card -->
    <section id="envSection" class="card" style="display: none;">
        <div class="card-header">
            <h2>Environment Variables</h2>
            <div class="bulk-edit-toggle">
                <span class="toggle-label">Bulk Edit</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="bulkEditToggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </div>

        <!-- Grid and Bulk Area are direct children, managing their own padding/margin -->
        <div id="envGrid" class="env-grid"></div> <!-- Grid container -->
        <textarea id="bulkEditArea" class="bulk-textarea" style="display: none;"
                    placeholder="KEY=value
ANOTHER_KEY=another_value"></textarea>

        <div class="card-footer">
            <button class="btn btn-primary" id="addVariableBtn">
                <i data-lucide="plus"></i>
                Add Variable
            </button>
            <button class="btn btn-success" id="saveEnvBtn">
                <i data-lucide="save"></i>
                Save Changes
            </button>
        </div>
    </section>
</main>

<script>
    // Initialize Lucide icons
    lucide.createIcons();

    // Cache DOM elements
    const logsTab = document.getElementById('logsTab');
    const envTab = document.getElementById('envTab');
    const logsSection = document.getElementById('logsSection');
    const envSection = document.getElementById('envSection');
    const logsContent = document.getElementById('logsContent');
    const refreshLogsBtn = document.getElementById('refreshLogsBtn');
    const bulkEditToggle = document.getElementById('bulkEditToggle');
    const envGrid = document.getElementById('envGrid');
    const bulkEditArea = document.getElementById('bulkEditArea');
    const addVariableBtn = document.getElementById('addVariableBtn');
    const saveEnvBtn = document.getElementById('saveEnvBtn');
    const restartBtn = document.getElementById('restartBtn');
    const rebuildBtn = document.getElementById('rebuildBtn');

    /* Notification Display */
    const showNotification = (message, type = 'success') => {
        // Remove existing notification first
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Set timeout to remove the notification
        setTimeout(() => {
             // Optional: Add a class or style for fade out animation
             // notification.style.animation = 'slideDown 0.4s ease-in forwards';
             notification.style.opacity = '0'; // Simple fade out
             notification.style.transition = 'opacity 0.4s ease';
             setTimeout(() => notification.remove(), 400); // Remove after fade
        }, 3000); // Notification visible for 3 seconds
    };

    /* Tab Switching */
    const switchTab = (activeTab) => {
        [logsTab, envTab].forEach(tab => tab.classList.remove('active'));
        activeTab.classList.add('active');
        if (activeTab === logsTab) {
            logsSection.style.display = 'block';
            envSection.style.display = 'none';
            // Resume log polling if necessary
            if (document.visibilityState === 'visible' && !logInterval) {
                 startLogPolling();
            }
        } else {
            logsSection.style.display = 'none';
            envSection.style.display = 'block';
             // Pause log polling
             stopLogPolling();
            // Load env only once when tab is first opened or if it hasn't loaded yet
            if (!envSection.dataset.loaded) {
                loadEnv();
                envSection.dataset.loaded = 'true';
            }
        }
    };

    logsTab.addEventListener('click', () => switchTab(logsTab));
    envTab.addEventListener('click', () => switchTab(envTab));

    /* Load Application Logs */
    let logInterval = null; // Variable to hold the interval ID

    const loadLogs = async (isManualRefresh = false) => {
        // Don't show loading state on auto-refresh unless it's manual
        if (isManualRefresh) {
            refreshLogsBtn.disabled = true;
            const icon = refreshLogsBtn.querySelector('i');
            if (icon) icon.classList.add('spin'); // Add spin class
        }
        try {
            const res = await fetch('/api/logs');
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
            const data = await res.json();
            // const currentYear = new Date().getFullYear().toString(); // Keep if needed
            const cleaned = (data.logs || '') // Handle potentially null/undefined logs
                .split('\n')
                .map(line => {
                    line = line.replace(/[\u0000-\u0008\u000B-\u001F\u007F-\u009F]/g, '');
                    line = line.replace(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?\s*/, '');
                    return line.trim();
                })
                .filter(line => line.length > 0)
                .join('\n');

            // Only update if content has changed to avoid unnecessary reflows
            if (logsContent.textContent !== cleaned) {
                 logsContent.textContent = cleaned || "No logs found or logs are empty.";
                 // Scroll to bottom only if user is already near the bottom (optional heuristic)
                 // const isScrolledToBottom = logsContent.scrollHeight - logsContent.clientHeight <= logsContent.scrollTop + 10;
                 // if (isScrolledToBottom) {
                 //     logsContent.scrollTop = logsContent.scrollHeight;
                 // }
            }
        } catch (error) {
            // Only show error in console for auto-refresh, show in UI for manual
            if (isManualRefresh) {
                 logsContent.textContent = `Error loading logs: ${error.message}`;
            }
            console.error('Logs error:', error);
        } finally {
             if (isManualRefresh) {
                 refreshLogsBtn.disabled = false;
                 const icon = refreshLogsBtn.querySelector('i');
                 if (icon) icon.classList.remove('spin'); // Remove spin class
             }
        }
    };

    const stopLogPolling = () => {
        if (logInterval) {
            clearInterval(logInterval);
            logInterval = null;
            console.log("Log polling stopped.");
        }
    }

    const startLogPolling = () => {
         stopLogPolling(); // Clear any existing interval first
         if (logsTab.classList.contains('active') && document.visibilityState === 'visible') {
             console.log("Starting log polling...");
             loadLogs(); // Load immediately
             logInterval = setInterval(() => loadLogs(false), 15000); // Poll every 15 seconds
         } else {
             console.log("Log polling not started (tab not active or page hidden).");
         }
    }


    refreshLogsBtn.addEventListener('click', () => loadLogs(true)); // Pass true for manual refresh

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopLogPolling();
        } else {
             startLogPolling(); // Will only start if logs tab is active
        }
    });


    /* Environment Variables Handling */
    const loadEnv = async () => {
        // Indicate loading state
        envGrid.innerHTML = `<p style="grid-column: 1 / -1; padding: 1rem 0; color: var(--text-muted-color);">Loading variables...</p>`; // Span full width
        try {
            const res = await fetch('/api/env');
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
            const data = await res.json();
            renderEnvVars(data.env);
        } catch (error) {
            showNotification(`Failed to load environment variables: ${error.message}`, 'error');
            envGrid.innerHTML = `<p style="grid-column: 1 / -1; color: var(--danger-color); padding: 1rem 0;">Failed to load variables. Please try refreshing.</p>`; // Span full width
            envSection.dataset.loaded = 'false'; // Allow reloading on next tab visit
        }
    };

    const renderEnvVars = (envVars = []) => {
        envVars.sort((a, b) => a.key.localeCompare(b.key));

        envGrid.innerHTML = envVars.length > 0 ? envVars.map((env, index) => `
            <input type="text" class="input-field env-key" value="${env.key || ''}" placeholder="Key" data-index="${index}" aria-label="Environment variable key ${index + 1}">
            <input type="text" class="input-field env-value" value="${env.value || ''}" placeholder="Value" data-index="${index}" aria-label="Environment variable value ${index + 1}">
        `).join('') : `<p style="grid-column: 1 / -1; padding: 1rem 0; color: var(--text-muted-color);">No environment variables defined. Click 'Add Variable' to start.</p>`; // Span full width
        // Ensure grid is visible and bulk is hidden when rendering grid
         envGrid.style.display = 'grid';
         bulkEditArea.style.display = 'none';
         addVariableBtn.style.display = 'inline-flex';
    };

    const addNewVariable = () => {
        const placeholder = envGrid.querySelector('p');
        if (placeholder) placeholder.remove();

        const index = envGrid.querySelectorAll('.env-key').length;
        const keyInput = document.createElement('input');
        keyInput.type = 'text';
        keyInput.className = 'input-field env-key';
        keyInput.placeholder = 'Key';
        keyInput.dataset.index = index;
        keyInput.setAttribute('aria-label', `New environment variable key ${index + 1}`);

        const valueInput = document.createElement('input');
        valueInput.type = 'text';
        valueInput.className = 'input-field env-value';
        valueInput.placeholder = 'Value';
        valueInput.dataset.index = index;
        valueInput.setAttribute('aria-label', `New environment variable value ${index + 1}`);

        // Check if the grid is in single-column mode (mobile)
        const isSingleColumn = window.getComputedStyle(envGrid).gridTemplateColumns.split(' ').length === 1;

        if (isSingleColumn) {
             // Append key and value separately in single column mode
             envGrid.appendChild(keyInput);
             envGrid.appendChild(valueInput);
        } else {
             // Append both to let the grid place them
             envGrid.append(keyInput, valueInput);
        }

        keyInput.focus();
    };


    addVariableBtn.addEventListener('click', addNewVariable);

    // Toggle Bulk Edit mode
    bulkEditToggle.addEventListener('change', function () {
        if (this.checked) {
            const envVarsText = Array.from(envGrid.querySelectorAll('.env-key'))
                .map(keyInput => {
                    // Find the corresponding value input reliably
                    const index = keyInput.dataset.index;
                    const valueInput = envGrid.querySelector(`.env-value[data-index="${index}"]`);
                    const key = keyInput.value.trim();
                    const value = valueInput ? valueInput.value.trim() : '';
                    return (key) ? `${key}=${value}` : null;
                })
                 .filter(line => line !== null)
                 .sort()
                 .join('\n');
            bulkEditArea.value = envVarsText;
            envGrid.style.display = 'none';
            addVariableBtn.style.display = 'none';
            bulkEditArea.style.display = 'block';
            bulkEditArea.focus();
        } else {
            // Parse bulk area and render back to grid
            const envVars = bulkEditArea.value.split('\n')
                .map(line => line.trim())
                .filter(line => line && line.includes('='))
                .map(line => {
                    const separatorIndex = line.indexOf('=');
                    const key = line.substring(0, separatorIndex).trim();
                    const value = line.substring(separatorIndex + 1).trim();
                    return { key, value };
                })
                 .filter(env => env.key);

            renderEnvVars(envVars); // This function now handles showing grid/hiding bulk
        }
    });

    /* Save Environment Variables */
    const saveEnvChanges = async () => {
        saveEnvBtn.disabled = true;
        const originalHTML = saveEnvBtn.innerHTML; // Store original button content
        saveEnvBtn.innerHTML = `<i data-lucide="loader-2" class="spin"></i> Saving...`;
        lucide.createIcons({ icons: { 'loader-2': saveEnvBtn.querySelector('.spin') } });

        try {
            let envVars = [];
            if (bulkEditToggle.checked) {
                envVars = bulkEditArea.value.split('\n')
                    .map(line => line.trim())
                    .filter(line => line && line.includes('='))
                    .map(line => {
                         const separatorIndex = line.indexOf('=');
                         const key = line.substring(0, separatorIndex).trim();
                         const value = line.substring(separatorIndex + 1).trim();
                        return { key, value };
                    })
                     .filter(env => env.key);
            } else {
                const keyInputs = envGrid.querySelectorAll('.env-key');
                keyInputs.forEach(keyInput => {
                     // Find the corresponding value input reliably
                     const index = keyInput.dataset.index;
                     const valueInput = envGrid.querySelector(`.env-value[data-index="${index}"]`);
                    const key = keyInput.value.trim();
                    const value = valueInput ? valueInput.value.trim() : '';
                    if (key) {
                        envVars.push({ key, value });
                    }
                });
            }

            const res = await fetch('/api/env', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({env: envVars})
            });

            if (!res.ok) {
                 const errorData = await res.json().catch(() => ({ error: 'Failed to parse error response' }));
                 throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            if (data.success) {
                showNotification('Environment variables saved successfully');
                // Re-render the grid view to ensure consistency and sorting, even if saving from bulk
                 renderEnvVars(envVars);
                 // Uncheck toggle if save was successful in bulk mode
                 if (bulkEditToggle.checked) {
                      bulkEditToggle.checked = false;
                      // RenderEnvVars already handles switching view
                 }

            } else {
                throw new Error(data.error || 'Failed to save environment variables');
            }
        } catch (error) {
            showNotification(`Error saving: ${error.message}`, 'error');
            console.error('Save ENV error:', error);
        } finally {
            saveEnvBtn.disabled = false;
            saveEnvBtn.innerHTML = originalHTML; // Restore original content
            lucide.createIcons(); // Re-render all icons
        }
    };

    saveEnvBtn.addEventListener('click', saveEnvChanges);

    /* Handle Restart & Rebuild Actions */
    const handleAction = async (action, button) => {
         button.disabled = true;
         const originalHTML = button.innerHTML;
         button.innerHTML = `<i data-lucide="loader-2" class="spin"></i> ${action.charAt(0).toUpperCase() + action.slice(1)}ing...`;
         lucide.createIcons({ icons: { 'loader-2': button.querySelector('.spin') } });

        try {
            const res = await fetch(`/api/${action}`, {method: 'POST'});

            if (!res.ok) {
                 const errorData = await res.json().catch(() => ({ error: 'Failed to parse error response' }));
                 throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            if (data.success) {
                showNotification(`${action.charAt(0).toUpperCase() + action.slice(1)} initiated successfully.`);
                 switchTab(logsTab); // Switch to logs tab
                 setTimeout(() => loadLogs(true), 500); // Refresh logs manually after a short delay
            } else {
                throw new Error(data.error || `${action} failed`);
            }
        } catch (error) {
            showNotification(`Error during ${action}: ${error.message}`, 'error');
            console.error(`${action} error:`, error);
        } finally {
             button.disabled = false;
             button.innerHTML = originalHTML; // Restore original content
             lucide.createIcons();
        }
    };

    restartBtn.addEventListener('click', () => handleAction('restart', restartBtn));
    rebuildBtn.addEventListener('click', () => handleAction('rebuild', rebuildBtn));

    // Initial load for the default tab and start polling
    startLogPolling();

</script>
</body>
</html>                                                                                                                                                                                                                                                                                                                                                        templates/login.html                                                                                000666  000000  000000  00000020246 14771510021 013226  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Logs Viewer Application Login">
    <link rel="icon" type="image/svg+xml" href="https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/lock.svg">
    <title>{{ app_name | default('Admin Dashboard') }} - Login</title>
    <style>
        *, *::before, *::after {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #43cea2, #185a9d); /* Vibrant gradient background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            overflow: hidden; /* Prevent scrollbars for the background animation */
        }

        /* Animated background circles */
        body::before,
        body::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            filter: blur(50px);
            animation: float 6s infinite alternate;
        }

        body::before {
            top: 10%;
            left: 10%;
        }

        body::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 2s;
        }

        @keyframes float {
            from {
                transform: translateY(0) scale(1);
                opacity: 0.8;
            }
            to {
                transform: translateY(-20px) scale(1.1);
                opacity: 0.5;
            }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95); /* Semi-transparent white background */
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2); /* Deeper shadow */
            width: 100%;
            max-width: 400px;
            text-align: center;
            backdrop-filter: blur(10px); /* Subtle blur behind the container */
            position: relative;
            z-index: 10; /* Ensure it's above the background circles */
            transition: transform 0.3s ease-in-out;
        }

        .login-container:hover {
            transform: scale(1.02);
        }

        .login-image {
            width: 72px;
            height: 72px;
            margin: 0 auto 25px;
            display: block;
            color: #fff;
            background-color: #007bff;
            border-radius: 50%;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .login-image svg {
            display: block;
            width: 100%;
            height: 100%;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
            font-size: 2.2rem;
        }

        .login-header p {
            color: #666;
            margin: 0;
            font-size: 1rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-size: 1.1rem;
            font-weight: 500;
            text-align: left;
        }

        .form-control {
            width: 100%;
            padding: 14px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
        }

        .btn {
            width: 100%;
            background: #007bff;
            color: #fff;
            padding: 14px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: background 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            background: #0069d9;
        }

        .btn:active {
            background: #0056b3;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 1rem;
            text-align: left;
        }

        .alert-error {
            background-color: #fdecea;
            border: 1px solid #f1aeb5;
            color: #842029;
        }

        .alert-success {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }

        @media (max-width: 576px) {
            .login-container {
                padding: 30px;
                max-width: 95%;
            }

            .login-header h2 {
                font-size: 2rem;
            }

            .login-header p {
                font-size: 0.9rem;
            }

            .form-group label {
                font-size: 1rem;
            }

            .form-control, .btn, .alert {
                font-size: 0.95rem;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
<div class="login-container">
    <div class="login-header">
        <div class="login-image">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="lucide lucide-lock">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
            </svg>
        </div>
        <h2>{{ app_name | default('Admin Dashboard') }}</h2>
        <p>Securely access your account</p>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    {% for category, message in messages %}
    <div class="alert alert-{{ category if category != 'error' else 'error' }}">
        {{ message }}
    </div>
    {% endfor %}
    {% endif %}
    {% endwith %}

    <form method="post" autocomplete="off">
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" class="form-control" placeholder="Enter your username"
                   required autofocus>
        </div>

        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" class="form-control" placeholder="Enter your password"
                   required>
        </div>

        <button type="submit" class="btn">Log In</button>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector('form');
        form.addEventListener('submit', function (e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                e.preventDefault();
                const alert = document.createElement('div');
                alert.className = 'alert alert-error';
                alert.textContent = 'Please enter both username and password';

                const existingAlert = document.querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                form.insertBefore(alert, form.firstChild);
            }
        });
    });
</script>
</body>
</html>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          