#!/usr/bin/env python3
"""
Test emoji preservation in the cleaning function
"""
import re

def clean_docker_logs(raw_logs):
    """
    Clean Docker log format and ANSI escape sequences from logs.
    Docker logs come with headers like: \x01\x00\x00\x00\x00\x00\x00[LENGTH]
    followed by ANSI escape sequences.
    """
    if not raw_logs:
        return ""
    
    lines = raw_logs.split('\n')
    cleaned_lines = []
    
    for i, line in enumerate(lines):
        if not line:
            continue
            
        # Remove Docker log format headers (8 bytes: stream type + 7 bytes length)
        # Docker format: [stream_type][0x00][0x00][0x00][length_bytes][log_data]
        if len(line) >= 8 and line[0] in ['\x01', '\x02']:  # stdout or stderr
            # Skip the 8-byte Docker header
            line = line[8:]
        
        # Remove ANSI escape sequences completely
        # This regex removes ESC[...m (colors), ESC[...J (clear), ESC[...K (erase), etc.
        line = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', line)
        
        # Remove other control characters except newlines, tabs, and Unicode characters (emojis)
        # Be more careful to preserve Unicode characters (emojis) which are above \u007F
        line = re.sub(r'[\x00-\x08\x0B-\x1F\x7F]', '', line)  # Removed \x80-\x9F range to preserve Unicode
        
        # Clean up any remaining artifacts and extra whitespace
        line = line.strip()
        
        # Check if this line has a timestamp (new log entry)
        has_timestamp = re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', line)
        
        if has_timestamp:
            # Remove various ANSI artifact patterns from timestamped lines, but preserve emojis
            # Use a simpler approach: remove single ASCII letters immediately before emojis

            # First, handle the specific case of single letters before emojis: "p✅" -> "✅"
            emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\*********-\U000027BF\U0001F900-\U0001F9FF]'
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z](' + emoji_pattern + ')', r'\1\2', line)

            # Then handle other ANSI artifacts
            # 1. Single letter followed by space: "p ", "b ", "c " (but only if not followed by emoji)
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\s+', r'\1', line)
            # 2. Letter followed by numbers and space: "b0 ", "p8 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\d+\s+', r'\1', line)
            # 3. Letter followed by quote and numbers: "p'9 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]\'\d+\s+', r'\1', line)
            # 4. Letter combinations like "po8 "
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]{1,2}\d*\s+', r'\1', line)
            # 5. Handle standalone single ASCII letters at the end of timestamp line
            line = re.sub(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)[a-zA-Z]$', r'\1', line)
            
            # If after cleaning the timestamp line is empty or just whitespace, 
            # check if the next line should be merged with this timestamp
            line_after_timestamp = re.sub(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', '', line).strip()
            if not line_after_timestamp and i + 1 < len(lines):
                # Look at the next line to see if it's a continuation
                next_line = lines[i + 1]
                # Remove Docker headers from next line
                if len(next_line) >= 8 and next_line[0] in ['\x01', '\x02']:
                    next_line = next_line[8:]
                # Clean ANSI from next line
                next_line = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', next_line)
                next_line = re.sub(r'[\x00-\x08\x0B-\x1F\x7F]', '', next_line)
                next_line = next_line.strip()
                
                # If next line doesn't have timestamp, merge it
                if next_line and not re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+', next_line):
                    timestamp_part = re.match(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z\s+)', line).group(1)
                    line = timestamp_part + next_line
                    # Skip the next line since we merged it
                    lines[i + 1] = ""
        
        if line:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def test_emoji_preservation():
    print("=== EMOJI PRESERVATION TEST ===")
    
    # Test with logs that contain emojis (simulating what comes from CapRover)
    test_logs_with_emojis = """2025-07-27T11:51:44.840423258Z ✅ Session management initialized successfully - database is clean
2025-07-27T11:51:44.840438367Z ✅ Session management initialized - all existing sessions cleared
2025-07-27T11:51:44.840460568Z ✅ Scheduled session cleanup every 30 minutes
2025-07-27T11:51:44.840460568Z 🚀 Server starting - initializing session management
2025-07-27T11:51:44.840460568Z ⏳ Session management will initialize after database connection
2025-07-27T11:51:44.840460568Z 📊 Found 0 existing sessions in database"""

    print("Original logs with emojis:")
    print(test_logs_with_emojis)
    print()
    
    # Clean the logs
    cleaned = clean_docker_logs(test_logs_with_emojis)
    
    print("After cleaning:")
    print(cleaned)
    print()
    
    # Check if emojis are preserved
    emojis = ['✅', '🚀', '⏳', '📊']
    print("Emoji preservation check:")
    for emoji in emojis:
        original_count = test_logs_with_emojis.count(emoji)
        cleaned_count = cleaned.count(emoji)
        status = "✅ PRESERVED" if original_count == cleaned_count else "❌ LOST"
        print(f"{emoji}: {original_count} → {cleaned_count} {status}")
    
    # Test with Docker headers and ANSI artifacts
    print("\n=== DOCKER HEADER + ANSI ARTIFACT TEST ===")
    
    # Simulate logs with Docker headers and ANSI artifacts
    docker_logs_with_emojis = """\x01\x00\x00\x00\x00\x00\x00V2025-07-27T11:51:44.840423258Z p✅ Session management initialized successfully
\x01\x00\x00\x00\x00\x00\x00V2025-07-27T11:51:44.840438367Z b🚀 Server starting - initializing session management"""
    
    print("Docker logs with headers and artifacts:")
    print(repr(docker_logs_with_emojis))
    print()
    
    cleaned_docker = clean_docker_logs(docker_logs_with_emojis)
    print("After cleaning Docker logs:")
    print(cleaned_docker)
    print()
    
    # Check emoji preservation in Docker logs
    print("Docker logs emoji preservation:")
    for emoji in ['✅', '🚀']:
        if emoji in cleaned_docker:
            print(f"{emoji}: ✅ PRESERVED")
        else:
            print(f"{emoji}: ❌ LOST")

if __name__ == "__main__":
    test_emoji_preservation()
