2025-03-16 16:36:07,700 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:33]
2025-03-16 16:37:10,520 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:33]
2025-03-16 16:39:40,061 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:33]
2025-03-16 16:40:31,608 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:35]
2025-03-16 16:41:15,563 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:35]
2025-03-16 16:42:23,363 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:36]
2025-03-16 16:42:56,328 WARNING: Failed login attempt for user hamabtw [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:79]
2025-03-16 16:43:08,354 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-16 16:43:28,907 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-16 16:45:23,243 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 16:46:42,703 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:77]
2025-03-16 16:47:02,293 INFO: Environment variables updated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:141]
2025-03-16 16:47:43,584 INFO: App restart initiated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:153]
2025-03-16 16:47:48,755 INFO: App restart initiated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:153]
2025-03-16 16:47:48,898 ERROR: Error fetching logs: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')) [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:110]
2025-03-16 16:51:27,435 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 16:51:40,457 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-16 16:52:29,593 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 16:56:29,606 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 16:57:20,435 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 16:57:34,001 INFO: App restart initiated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:152]
2025-03-16 16:58:05,682 INFO: Environment variables updated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:140]
2025-03-16 16:58:38,439 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 17:00:19,500 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 17:02:19,338 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 17:04:01,000 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 17:08:01,584 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-16 17:08:47,925 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-16 17:09:05,922 INFO: Environment variables updated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:140]
2025-03-16 17:09:35,417 INFO: Environment variables updated: {'status': 100, 'description': 'Updated App Definition Saved', 'data': {}} [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:140]
2025-03-17 14:19:23,917 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-17 14:19:41,382 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-17 14:19:55,999 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-17 14:20:02,541 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:76]
2025-03-17 14:22:21,700 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:37]
2025-03-28 13:03:40,064 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-03-28 13:48:31,384 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:78]
2025-03-28 13:51:31,623 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-03-28 14:28:17,003 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-03-28 14:31:43,844 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-03-28 15:12:37,130 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-04-10 16:01:06,152 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-04-10 16:01:26,135 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:78]
2025-04-10 16:01:49,997 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-04-21 10:29:49,307 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-04-21 10:30:32,488 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:78]
2025-07-27 11:47:32,890 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-07-27 11:48:10,970 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:78]
2025-07-27 11:48:17,463 INFO: Application startup [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:39]
2025-07-27 11:48:40,043 INFO: User hamabtw logged in successfully [in C:\Users\<USER>\PycharmProjects\caprover_api\main.py:78]
