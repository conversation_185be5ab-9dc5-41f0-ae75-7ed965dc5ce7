import json
import os

import requests
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
from caprover_api import caprover_api
from functools import wraps
from datetime import timedelta
import logging
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

# Initialize Flask app
app = Flask(__name__)

# Load env variables
load_dotenv()

# Configuration
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', ''),
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)
)

# Configure logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/application.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('Application startup')

# CapRover API configuration
APP_NAME = os.environ.get('APP_NAME', '')
CAPROVER_URL = os.environ.get('CAPROVER_URL', '')
CAPROVER_PASSWORD = os.environ.get('CAPROVER_PASSWORD', '')

# Initialize the Caprover API client
cap = caprover_api.CaproverAPI(
    dashboard_url=CAPROVER_URL,
    password=CAPROVER_PASSWORD
)

# Admin credentials (should be stored securely in environment variables)
ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '')
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', '')


# Simple login_required decorator
def login_required(f):
    @wraps(f)
    def wrapped(*args, **kwargs):
        if not session.get("logged_in"):
            flash("Please login first", "error")
            return redirect(url_for("login"))
        return f(*args, **kwargs)

    return wrapped


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")

        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session["logged_in"] = True
            session.permanent = True
            app.logger.info(f"User {username} logged in successfully")
            return redirect(url_for("dashboard"))
        else:
            app.logger.warning(f"Failed login attempt for user {username}")
            flash("Invalid credentials", "error")
    return render_template("login.html", app_name=APP_NAME)


@app.route("/logout")
@login_required
def logout():
    session.pop("logged_in", None)
    flash("Logged out successfully", "success")
    return redirect(url_for("login"))


@app.route("/")
@login_required
def dashboard():
    return render_template("dashboard.html", app_name=APP_NAME)


@app.route("/api/logs", methods=["GET"])
@login_required
def api_logs():
    try:
        response = cap.session.get(
            cap._build_url(f"/api/v2/user/apps/appData/{APP_NAME}/logs"),
            headers=cap.headers
        )
        logs = cap._check_errors(response.json()).get("data")
        return jsonify(logs)
    except Exception as e:
        app.logger.error(f"Error fetching logs: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/env", methods=["GET", "POST"])
@login_required
def api_env():
    if request.method == "GET":
        try:
            # Retrieve app configuration and extract environment variables.
            app_data = cap.get_app(APP_NAME)
            env_vars = app_data.get("envVars", {})
            return jsonify({"success": True, "env": env_vars})
        except Exception as e:
            app.logger.error(f"Error fetching env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    else:
        data = request.get_json()
        if not data or "env" not in data:
            return jsonify({"success": False, "error": "Invalid request data"}), 400

        arr = data.get("env")
        try:
            app_data = cap.get_app(APP_NAME)
            app_data["envVars"] = arr

            response = cap.session.post(
                cap._build_url("/api/v2/user/apps/appDefinitions/update"),
                headers=cap.headers, data=json.dumps(app_data)
            )
            result = cap._check_errors(response.json())
            app.logger.info(f"Environment variables updated: {result}")
            return jsonify({"success": True})
        except Exception as e:
            app.logger.error(f"Error updating env vars: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/restart", methods=["POST"])
@login_required
def api_restart():
    try:
        result = cap.update_app(APP_NAME, instance_count=1)
        app.logger.info(f"App restart initiated: {result}")
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error restarting app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/rebuild", methods=["POST"])
@login_required
def api_rebuild():
    try:
        url = os.environ.get('REBUILD_URL', '')
        if url == '':
            return jsonify({"success": False, "error": "this app doesn't support rebuild!"}), 500
        result = requests.post(url)
        return jsonify({"success": True, "result": result})
    except Exception as e:
        app.logger.error(f"Error rebuilding app: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f'Server Error: {str(error)}')
    return render_template('500.html'), 500


if __name__ == "__main__":
    # In production, use a proper WSGI server like Gunicorn
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
