<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8813a838-dbfb-472a-bed2-020bc596fa0e" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="HTML File" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2tXiO3zqySXt3ga3XchvtxzXsHB" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultHtmlFileTemplate" value="HTML File" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$USER_HOME$/Documents/Cline/MCP/mcp-browser-use" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="restartRequiresConfirmation" value="false" />
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="caprover_api" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="openrouter" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="caprover_api" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/openrouter.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.openrouter" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8813a838-dbfb-472a-bed2-020bc596fa0e" name="Default Changelist" comment="" />
      <created>1740504784586</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740504784586</updated>
      <workItem from="1740504829141" duration="1487000" />
      <workItem from="1741290267947" duration="1571000" />
      <workItem from="1741463894838" duration="828000" />
      <workItem from="1741948722931" duration="18015000" />
      <workItem from="1741983601461" duration="2532000" />
      <workItem from="1741993495385" duration="1907000" />
      <workItem from="1742049014635" duration="3869000" />
      <workItem from="1742055202376" duration="390000" />
      <workItem from="1742057314229" duration="403000" />
      <workItem from="1742136785908" duration="4770000" />
      <workItem from="1742148170076" duration="668000" />
      <workItem from="1742214290613" duration="756000" />
      <workItem from="1742217551613" duration="241000" />
      <workItem from="1742248832293" duration="1130000" />
      <workItem from="1742252700839" duration="449000" />
      <workItem from="1742307784012" duration="603000" />
      <workItem from="1743028244623" duration="4000" />
      <workItem from="1743163309272" duration="3905000" />
      <workItem from="1743453632785" duration="624000" />
      <workItem from="1744297240770" duration="292000" />
      <workItem from="1744364087911" duration="26000" />
      <workItem from="1745227758944" duration="1038000" />
      <workItem from="1745833296768" duration="7000" />
      <workItem from="1745833313488" duration="31000" />
      <workItem from="1753613218147" duration="504000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/caprover_api$main.coverage" NAME="main Coverage Results" MODIFIED="1753613296468" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/caprover_api$openrouter.coverage" NAME="openrouter Coverage Results" MODIFIED="1741290931334" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>