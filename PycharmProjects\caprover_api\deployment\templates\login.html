<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Logs Viewer Application Login">
    <link rel="icon" type="image/svg+xml" href="https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/lock.svg">
    <title>{{ app_name | default('Admin Dashboard') }} - Login</title>
    <style>
        *, *::before, *::after {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(45deg, #43cea2, #185a9d); /* Vibrant gradient background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            overflow: hidden; /* Prevent scrollbars for the background animation */
        }

        /* Animated background circles */
        body::before,
        body::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            filter: blur(50px);
            animation: float 6s infinite alternate;
        }

        body::before {
            top: 10%;
            left: 10%;
        }

        body::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 2s;
        }

        @keyframes float {
            from {
                transform: translateY(0) scale(1);
                opacity: 0.8;
            }
            to {
                transform: translateY(-20px) scale(1.1);
                opacity: 0.5;
            }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95); /* Semi-transparent white background */
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2); /* Deeper shadow */
            width: 100%;
            max-width: 400px;
            text-align: center;
            backdrop-filter: blur(10px); /* Subtle blur behind the container */
            position: relative;
            z-index: 10; /* Ensure it's above the background circles */
            transition: transform 0.3s ease-in-out;
        }

        .login-container:hover {
            transform: scale(1.02);
        }

        .login-image {
            width: 72px;
            height: 72px;
            margin: 0 auto 25px;
            display: block;
            color: #fff;
            background-color: #007bff;
            border-radius: 50%;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .login-image svg {
            display: block;
            width: 100%;
            height: 100%;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
            font-size: 2.2rem;
        }

        .login-header p {
            color: #666;
            margin: 0;
            font-size: 1rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-size: 1.1rem;
            font-weight: 500;
            text-align: left;
        }

        .form-control {
            width: 100%;
            padding: 14px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
        }

        .btn {
            width: 100%;
            background: #007bff;
            color: #fff;
            padding: 14px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: background 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            background: #0069d9;
        }

        .btn:active {
            background: #0056b3;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 1rem;
            text-align: left;
        }

        .alert-error {
            background-color: #fdecea;
            border: 1px solid #f1aeb5;
            color: #842029;
        }

        .alert-success {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }

        @media (max-width: 576px) {
            .login-container {
                padding: 30px;
                max-width: 95%;
            }

            .login-header h2 {
                font-size: 2rem;
            }

            .login-header p {
                font-size: 0.9rem;
            }

            .form-group label {
                font-size: 1rem;
            }

            .form-control, .btn, .alert {
                font-size: 0.95rem;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
<div class="login-container">
    <div class="login-header">
        <div class="login-image">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="lucide lucide-lock">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
            </svg>
        </div>
        <h2>{{ app_name | default('Admin Dashboard') }}</h2>
        <p>Securely access your account</p>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    {% for category, message in messages %}
    <div class="alert alert-{{ category if category != 'error' else 'error' }}">
        {{ message }}
    </div>
    {% endfor %}
    {% endif %}
    {% endwith %}

    <form method="post" autocomplete="off">
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" class="form-control" placeholder="Enter your username"
                   required autofocus>
        </div>

        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" class="form-control" placeholder="Enter your password"
                   required>
        </div>

        <button type="submit" class="btn">Log In</button>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector('form');
        form.addEventListener('submit', function (e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                e.preventDefault();
                const alert = document.createElement('div');
                alert.className = 'alert alert-error';
                alert.textContent = 'Please enter both username and password';

                const existingAlert = document.querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                form.insertBefore(alert, form.firstChild);
            }
        });
    });
</script>
</body>
</html>