<!DOCTYPE html>
<html>
<head>
    <title>Log Test</title>
    <style>
        body { font-family: monospace; background: #000; color: #fff; padding: 20px; }
        .test-section { margin: 20px 0; border: 1px solid #333; padding: 10px; }
        pre { background: #111; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Log Processing Test</h1>
    
    <div class="test-section">
        <h2>Original Log Sample (what you see in Docker)</h2>
        <pre id="original">2025-07-27T03:19:51.457975454Z ⏳ Session management will initialize after database connection...
2025-07-27T03:19:54.541424428Z Database connected successfully
2025-07-27T03:19:54.541928483Z 🚀 Server starting - initializing session management...
2025-07-27T03:19:54.552857907Z 📊 Found 0 existing sessions in database
2025-07-27T03:19:54.556592248Z 🗑️ Hard deleted 0 sessions from database
2025-07-27T03:19:54.556635949Z 🧹 Clearing any in-memory sessions...
2025-07-27T03:19:54.556640107Z ✅ Session management initialized successfully - database is clean
2025-07-27T03:19:54.556643834Z ✅ Session management initialized - all existing sessions cleared
2025-07-27T03:19:54.556675632Z ✅ Scheduled session cleanup every 30 minutes</pre>
    </div>

    <div class="test-section">
        <h2>Corrupted Log Sample (what you see in web UI)</h2>
        <pre id="corrupted">2025-07-27T03:19:51.453408137Z p WebSocket server initialized with CORS origins: [
:2025-07-27T03:19:51.453416462Z   'http://localhost:3000',
:2025-07-27T03:19:51.453420229Z   'http://localhost:3005',
<2025-07-27T03:19:51.453423786Z   'https://www.tache-lik.tn'
!2025-07-27T03:19:51.453427302Z ]
O2025-07-27T03:19:51.457760685Z p Server is running on http://localhost:3000
>2025-07-27T03:19:51.457967629Z p WebSocket server is ready
c2025-07-27T03:19:51.457975454Z b3 Session management will initialize after database connection...
?2025-07-27T03:19:54.541424428Z Database connected successfully
Y2025-07-27T03:19:54.541928483Z p Server starting - initializing session management...
J2025-07-27T03:19:54.552857907Z p
Found 0 existing sessions in database
M2025-07-27T03:19:54.556592248Z po8 Hard deleted 0 sessions from database
G2025-07-27T03:19:54.556635949Z p'9 Clearing any in-memory sessions...
c2025-07-27T03:19:54.556640107Z b Session management initialized successfully - database is clean
b2025-07-27T03:19:54.556643834Z b Session management initialized - all existing sessions cleared
N2025-07-27T03:19:54.556675632Z b Scheduled session cleanup every 30 minutes</pre>
    </div>

    <div class="test-section">
        <h2>Test Current Processing</h2>
        <button onclick="testProcessing()">Test Log Processing</button>
        <pre id="processed"></pre>
    </div>

    <script>
        // Simulate the current processing
        function testProcessing() {
            const sampleLog = `2025-07-27T03:19:51.457975454Z ⏳ Session management will initialize after database connection...
2025-07-27T03:19:54.541424428Z Database connected successfully
2025-07-27T03:19:54.541928483Z 🚀 Server starting - initializing session management...
2025-07-27T03:19:54.552857907Z 📊 Found 0 existing sessions in database`;

            // Current processing
            const cleaned = sampleLog
                .split('\n')
                .map(line => {
                    line = line.replace(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?\s*/, '');
                    return line;
                })
                .filter(line => line.length > 0)
                .join('\n');

            document.getElementById('processed').textContent = cleaned;
        }
    </script>
</body>
</html>
