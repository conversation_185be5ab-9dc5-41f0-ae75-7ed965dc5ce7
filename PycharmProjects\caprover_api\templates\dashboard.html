<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="icon" type="image/svg+xml" href="https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/terminal.svg">
    <title>{{ app_name }}</title>
    <!-- Lucide icons -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js"></script>
    <style>
        :root {
            /* Updated Color Palette */
            --primary-color: #58a6ff; /* Brighter Blue */
            --primary-hover-color: #388bfd;
            --secondary-color: #161b22; /* Deeper Gray */
            --card-bg: #1f242c; /* Slightly lighter card bg */
            --border-color: #30363d;
            --text-color: #c9d1d9; /* Lighter text */
            --text-muted-color: #8b949e;
            --success-color: #3fb950;
            --success-hover-color: #2ea043;
            --danger-color: #f85149;
            --danger-hover-color: #da3633;
            --bg-gradient-from: #0d1117; /* Darker base */
            --bg-gradient-to: #161b22;
            --input-bg: #0d1117;
            --input-focus-border: var(--primary-color);
            --code-bg: #010409; /* Very dark for code */

            /* Sizing & Transitions */
            --border-radius-sm: 0.3rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --transition-duration: 0.2s;

            /* Helper for focus shadow - Calculate RGB from hex */
            --rgb-primary: 88, 166, 255; /* Calculated from #58a6ff */
        }

        /* Reset & Base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
            background: linear-gradient(145deg, var(--bg-gradient-from), var(--bg-gradient-to));
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header */
        header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .header-content {
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1.5rem; /* Increased padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem; /* Consistent gap */
        }

        .logo-section i {
            color: var(--primary-color); /* Use primary color */
            width: 1.75rem; /* Slightly larger */
            height: 1.75rem;
            flex-shrink: 0;
        }

        .logo-section h1 {
            font-size: 1.5rem; /* Adjusted size */
            font-weight: 600; /* Slightly less bold */
            background: linear-gradient(to right, #a1c4fd, var(--primary-color)); /* Subtle gradient */
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            white-space: nowrap;
        }

        .header-actions {
            display: flex;
            gap: 0.75rem; /* Consistent gap */
            flex-wrap: wrap;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center; /* Center content */
            gap: 0.5rem;
            padding: 0.6rem 1.2rem; /* Adjusted padding */
            border: 1px solid transparent; /* Base border */
            border-radius: var(--border-radius-md);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1; /* Prevent text jump */
            transition: background-color var(--transition-duration) ease, border-color var(--transition-duration) ease, opacity var(--transition-duration) ease, transform var(--transition-duration) ease;
            color: #ffffff;
            text-decoration: none; /* For link button */
        }

        .btn i {
            width: 1rem;
            height: 1rem;
            /* margin-right: 0.1rem; */ /* Removed for better centering with loader */
        }
         /* Specific style for icon + text */
         .btn i:not(:last-child) {
             margin-right: 0.3rem;
         }
         /* Specific style for loader */
         .btn .spin {
             margin-right: 0.4rem;
         }

        .btn:hover {
            filter: brightness(1.1); /* General hover effect */
        }

        .btn:active {
            transform: scale(0.98); /* Click effect */
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: #ffffff; /* Ensure contrast */
        }
        .btn-primary:hover {
            background-color: var(--primary-hover-color);
            border-color: var(--primary-hover-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        .btn-success:hover {
            background-color: var(--success-hover-color);
            border-color: var(--success-hover-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        .btn-danger:hover {
            background-color: var(--danger-hover-color);
            border-color: var(--danger-hover-color);
        }

        /* Icon-only button variant (used for refresh logs) */
        .btn-icon {
            padding: 0.5rem;
            background-color: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-muted-color);
        }
        .btn-icon:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            border-color: var(--text-muted-color);
            filter: none; /* Override general hover */
        }
        .btn-icon i {
            margin-right: 0; /* No extra margin for icon-only */
        }


        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            filter: grayscale(50%);
            transform: none; /* Disable active transform */
        }

        /* Navigation */
        nav {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .nav-content {
            max-width: 80rem;
            margin: 0 auto;
            padding: 0 1.5rem; /* Match header padding */
            display: flex;
            gap: 0.5rem; /* Reduced gap for tabs */
            flex-wrap: wrap;
            overflow-x: auto; /* Allow horizontal scroll on small screens */
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem; /* Adjusted padding */
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-muted-color);
            background: none;
            border: none;
            border-bottom: 2px solid transparent; /* Placeholder for alignment */
            cursor: pointer;
            position: relative;
            transition: color var(--transition-duration) ease, background-color var(--transition-duration) ease, border-color var(--transition-duration) ease;
            border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0; /* Top rounding */
            margin-bottom: -1px; /* Overlap border-bottom */
            white-space: nowrap; /* Prevent tab text wrapping */
        }

        .nav-btn:hover {
            color: var(--text-color);
            background-color: rgba(255, 255, 255, 0.03); /* Subtle hover */
        }

        .nav-btn.active {
            color: var(--primary-color);
            font-weight: 600;
            border-bottom-color: var(--primary-color); /* Active line indicator */
            background-color: var(--card-bg); /* Blend with card bg */
        }

        .nav-btn i {
             width: 1rem;
             height: 1rem;
        }

        /* Main Container */
        .container {
            max-width: 80rem;
            margin: 2.5rem auto; /* Increased top/bottom margin */
            padding: 0 1.5rem;
        }

        /* Card Styling */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin-bottom: 2.5rem; /* Increased spacing */
        }

        .card-header, .card-footer {
            padding: 1rem 1.5rem; /* Increased padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.1); /* Subtle distinction */
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap; /* Allow wrapping */
            gap: 0.75rem; /* Gap between items if they wrap */
        }

        .card-header h2 {
            font-size: 1.125rem; /* Slightly smaller */
            font-weight: 600;
            color: var(--text-color); /* Brighter header text */
            margin-right: auto; /* Push other items to the right */
        }

        .card-footer {
            border-top: 1px solid var(--border-color);
            border-bottom: none;
            background-color: transparent; /* Footer matches card body */
             /* Allow buttons to wrap is inherited */
             /* gap: 0.75rem; is inherited */
        }

        .card-body {
            padding: 1.5rem; /* Padding for content inside cards without specific padding */
        }

        /* Logs & Preformatted Text */
        pre {
            padding: 1.5rem;
            background: var(--code-bg); /* Darker background for contrast */
            color: #e6edf3; /* Lighter code text */
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.875rem;
            overflow: auto; /* Use auto for scrollbars only when needed */
            max-height: 600px;
            border-radius: var(--border-radius-md); /* Round corners */
            white-space: pre-wrap; /* Wrap long lines */
            word-break: break-all; /* Break long words/strings */
            line-height: 1.4; /* Better line spacing for readability */
        }

        /* Ensure emojis and special characters display properly in logs */
        pre#logsContent {
            font-feature-settings: "liga" 1, "calt" 1; /* Enable ligatures */
            text-rendering: optimizeLegibility;
        }
         /* Apply rounding if pre is direct child of card-body */
        .card-body > pre {
           margin: 0; /* Remove default pre margin if inside card-body */
           border-radius: var(--border-radius-md);
        }


        /* === Environment Variables Grid - UPDATED RULE === */
        .env-grid {
            display: grid;
            grid-template-columns: 1fr 1fr; /* Two columns for key/value */
            gap: 1rem;               /* Gap between key & value, and between rows */
            padding: 1.5rem;
            align-items: start;      /* <<< ADDED THIS LINE: Align items to the start (top) */
                                     /* Prevents vertical stretching, fixing height difference */
        }

        /* Removed the specific margin rule for even children as the main gap handles spacing better */
        /* Add specific margin to separate pairs */
        /* .env-grid .input-field:nth-child(even) { margin-bottom: 1.25rem; } */

        /* Removed the last-child rule as it's no longer needed without the nth-child rule */
        /* Remove bottom margin from the very last input */
        /* .env-grid .input-field:last-child { margin-bottom: 0; } */
        /* === Environment Variables Grid - END OF CHANGES === */


        .input-field {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background: var(--input-bg);
            color: var(--text-color);
            font-size: 0.9rem;
            transition: border-color var(--transition-duration) ease, box-shadow var(--transition-duration) ease;
            outline: none;
            /* No explicit height needed - let padding/font determine it */
        }

        .input-field::placeholder {
            color: var(--text-muted-color);
            opacity: 0.7;
        }

        .input-field:focus {
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 3px rgba(var(--rgb-primary), 0.3); /* Use RGB for alpha */
        }


        /* Bulk Edit */
        .bulk-edit-toggle {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .toggle-label {
            font-size: 0.875rem;
            color: var(--text-muted-color);
            white-space: nowrap; /* Prevent wrapping */
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 3rem; /* Larger */
            height: 1.75rem; /* Larger */
            flex-shrink: 0; /* Prevent shrinking */
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #30363d; /* Darker inactive */
            transition: var(--transition-duration) ease;
            border-radius: 1.75rem; /* Fully rounded */
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 1.25rem; /* Larger knob */
            width: 1.25rem;
            left: 0.25rem;
            bottom: 0.25rem;
            background-color: #ffffff;
            transition: var(--transition-duration) ease;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(1.25rem); /* Adjusted travel distance */
        }

        .bulk-textarea {
            /* Applied within the envSection, not inside card-body */
            display: block; /* Ensure it takes block layout */
            width: calc(100% - 3rem); /* Full width minus padding */
            margin: 1.5rem; /* Match card-body/env-grid padding */
            min-height: 20rem; /* Use min-height */
            background: var(--code-bg); /* Match pre background */
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: 1rem;
            color: var(--text-color);
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.9rem;
            /* margin: 1.5rem 0 0; Add top margin */
            resize: vertical;
            outline: none;
            transition: border-color var(--transition-duration) ease, box-shadow var(--transition-duration) ease;
        }

        .bulk-textarea::placeholder {
            color: var(--text-muted-color);
            opacity: 0.7;
        }

        .bulk-textarea:focus {
             border-color: var(--input-focus-border);
            box-shadow: 0 0 0 3px rgba(var(--rgb-primary), 0.3);
        }

        /* Notifications */
        .notification {
            position: fixed;
            bottom: 1.5rem; /* Position at bottom */
            right: 1.5rem;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius-md);
            color: #ffffff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.4s ease-out forwards; /* Add forwards */
            z-index: 50;
            font-size: 0.9rem;
            font-weight: 500;
            max-width: calc(100% - 3rem); /* Prevent overflow on small screens */
        }

        .notification.success {
            background-color: var(--success-color);
        }

        .notification.error {
            background-color: var(--danger-color);
        }

        @keyframes slideUp { /* Changed animation */
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        /* Add animation for slideOut if needed by JS */
         @keyframes slideDown {
             from {
                 transform: translateY(0);
                 opacity: 1;
             }
             to {
                 transform: translateY(100%);
                 opacity: 0;
             }
         }


        /* Custom Scrollbars (Webkit) */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: var(--secondary-color);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted-color);
        }


        /* Responsive */
        @media (max-width: 768px) {
            body { font-size: 15px; } /* Slightly adjust base size */

            .header-content {
                flex-direction: column;
                align-items: flex-start; /* Align items start */
                gap: 1.5rem; /* Increase gap when stacked */
                padding: 0 1rem;
            }

            .header-actions {
                width: 100%; /* Make actions full width */
                justify-content: center; /* Center buttons */
                gap: 0.5rem;
            }
             .header-actions .btn {
                 flex-grow: 1; /* Allow buttons to grow */
                 font-size: 0.85rem; /* Smaller text */
                 padding: 0.6rem 0.8rem;
             }

            .nav-content {
                 padding: 0 1rem; /* Slightly less padding */
            }
            .nav-btn {
                 padding: 0.75rem 0.8rem;
                 font-size: 0.85rem;
            }

             .container {
                 padding: 0 1rem;
                 margin: 1.5rem auto;
             }

            .card-header, .card-footer {
                padding: 1rem; /* Reduce padding on small screens */
                 /* Keep flex-direction row unless absolutely necessary */
                 /* align-items: center; */ /* Re-center items if they wrap */
            }
            .card-header h2 {
                font-size: 1.05rem;
            }
             /* Ensure toggle is aligned in stacked header */
            .card-header .bulk-edit-toggle {
                 margin-left: auto; /* Push toggle to the right if wraps */
                 /* margin-top: 0.5rem; */
                 /* width: 100%; */
                 /* justify-content: flex-end; */
            }


            .card-body { padding: 1rem; }
            pre { padding: 1rem; font-size: 0.8rem; }

            /* === ENV Grid Responsive - START === */
            .env-grid {
                grid-template-columns: 1fr; /* Single column on mobile */
                gap: 0.5rem; /* Smaller gap between key and value */
                padding: 1rem;
                /* align-items: start; is inherited and has no effect in single column */
            }

            /* Removed nth-child margin rule */
            /* .env-grid .input-field:nth-child(even) { margin-bottom: 1rem; } */
            /* === ENV Grid Responsive - END === */


            .input-field { font-size: 0.875rem; }

            .bulk-textarea {
                margin: 1rem;
                width: calc(100% - 2rem);
            }

            .card-footer .btn {
                width: 100%; /* Make footer buttons full width */
                /* font-size: 0.9rem; */ /* Optional: slightly larger font on mobile buttons */
            }
             .card-footer .btn:not(:first-child) {
                 /* margin-top: 0.5rem; */ /* Gap handles spacing */
             }

             .notification {
                 right: 1rem;
                 bottom: 1rem;
                 left: 1rem; /* Make notification wider on mobile */
                 text-align: center;
                 padding: 0.8rem 1rem;
                 font-size: 0.85rem;
                 max-width: calc(100% - 2rem);
             }
        }

         /* Loader animation */
         .spin { animation: spin 1s linear infinite; display: inline-block; }
         @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }

    </style>
</head>
<body>
<header>
    <div class="header-content">
        <div class="logo-section">
            <i data-lucide="terminal"></i>
            <h1>{{ app_name }}</h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" id="restartBtn">
                <i data-lucide="rotate-cw"></i>
                Restart
            </button>
            <button class="btn btn-success" id="rebuildBtn">
                <i data-lucide="refresh-cw"></i>
                Rebuild
            </button>
            <a href="/logout" class="btn btn-danger">
                <i data-lucide="log-out"></i>
                Logout
            </a>
        </div>
    </div>
</header>

<nav>
    <div class="nav-content">
        <button id="logsTab" class="nav-btn active">
            <i data-lucide="align-left"></i> <!-- Changed icon for variety -->
            Deployment
        </button>
        <button id="envTab" class="nav-btn">
            <i data-lucide="settings-2"></i> <!-- Changed icon for variety -->
            App Configs
        </button>
    </div>
</nav>

<main class="container">
    <!-- Logs Card -->
    <section id="logsSection" class="card">
        <div class="card-header">
            <h2>Application Logs</h2>
            <!-- Using icon-only button style -->
            <button class="btn btn-icon" id="refreshLogsBtn" title="Refresh Logs">
                <i data-lucide="refresh-cw"></i>
            </button>
        </div>
         <div class="card-body">
             <pre id="logsContent">Loading logs...</pre>
         </div>
    </section>

    <!-- Environment Variables Card -->
    <section id="envSection" class="card" style="display: none;">
        <div class="card-header">
            <h2>Environment Variables</h2>
            <div class="bulk-edit-toggle">
                <span class="toggle-label">Bulk Edit</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="bulkEditToggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </div>

        <!-- Grid and Bulk Area are direct children, managing their own padding/margin -->
        <div id="envGrid" class="env-grid"></div> <!-- Grid container -->
        <textarea id="bulkEditArea" class="bulk-textarea" style="display: none;"
                    placeholder="KEY=value
ANOTHER_KEY=another_value"></textarea>

        <div class="card-footer">
            <button class="btn btn-primary" id="addVariableBtn">
                <i data-lucide="plus"></i>
                Add Variable
            </button>
            <button class="btn btn-success" id="saveEnvBtn">
                <i data-lucide="save"></i>
                Save Changes
            </button>
        </div>
    </section>
</main>

<script>
    // Initialize Lucide icons
    lucide.createIcons();

    // Cache DOM elements
    const logsTab = document.getElementById('logsTab');
    const envTab = document.getElementById('envTab');
    const logsSection = document.getElementById('logsSection');
    const envSection = document.getElementById('envSection');
    const logsContent = document.getElementById('logsContent');
    const refreshLogsBtn = document.getElementById('refreshLogsBtn');
    const bulkEditToggle = document.getElementById('bulkEditToggle');
    const envGrid = document.getElementById('envGrid');
    const bulkEditArea = document.getElementById('bulkEditArea');
    const addVariableBtn = document.getElementById('addVariableBtn');
    const saveEnvBtn = document.getElementById('saveEnvBtn');
    const restartBtn = document.getElementById('restartBtn');
    const rebuildBtn = document.getElementById('rebuildBtn');

    /* Notification Display */
    const showNotification = (message, type = 'success') => {
        // Remove existing notification first
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Set timeout to remove the notification
        setTimeout(() => {
             // Optional: Add a class or style for fade out animation
             // notification.style.animation = 'slideDown 0.4s ease-in forwards';
             notification.style.opacity = '0'; // Simple fade out
             notification.style.transition = 'opacity 0.4s ease';
             setTimeout(() => notification.remove(), 400); // Remove after fade
        }, 3000); // Notification visible for 3 seconds
    };

    /* Tab Switching */
    const switchTab = (activeTab) => {
        [logsTab, envTab].forEach(tab => tab.classList.remove('active'));
        activeTab.classList.add('active');
        if (activeTab === logsTab) {
            logsSection.style.display = 'block';
            envSection.style.display = 'none';
            // Resume log polling if necessary
            if (document.visibilityState === 'visible' && !logInterval) {
                 startLogPolling();
            }
        } else {
            logsSection.style.display = 'none';
            envSection.style.display = 'block';
             // Pause log polling
             stopLogPolling();
            // Load env only once when tab is first opened or if it hasn't loaded yet
            if (!envSection.dataset.loaded) {
                loadEnv();
                envSection.dataset.loaded = 'true';
            }
        }
    };

    logsTab.addEventListener('click', () => switchTab(logsTab));
    envTab.addEventListener('click', () => switchTab(envTab));

    /* Load Application Logs */
    let logInterval = null; // Variable to hold the interval ID

    // Function to convert ANSI escape sequences to HTML while preserving emojis
    const ansiToHtml = (text) => {
        // ANSI color codes mapping with better terminal colors
        const ansiColors = {
            '30': 'color: #2e3440', // black
            '31': 'color: #bf616a', // red
            '32': 'color: #a3be8c', // green
            '33': 'color: #ebcb8b', // yellow
            '34': 'color: #81a1c1', // blue
            '35': 'color: #b48ead', // magenta
            '36': 'color: #88c0d0', // cyan
            '37': 'color: #e5e9f0', // white
            '90': 'color: #4c566a', // bright black (gray)
            '91': 'color: #bf616a', // bright red
            '92': 'color: #a3be8c', // bright green
            '93': 'color: #ebcb8b', // bright yellow
            '94': 'color: #81a1c1', // bright blue
            '95': 'color: #b48ead', // bright magenta
            '96': 'color: #8fbcbb', // bright cyan
            '97': 'color: #eceff4', // bright white
            '1': 'font-weight: bold',
            '2': 'opacity: 0.7', // dim
            '3': 'font-style: italic',
            '4': 'text-decoration: underline',
            '0': '' // reset
        };

        return text
            // First, escape HTML entities to prevent XSS but preserve Unicode
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            // Convert ANSI escape sequences to HTML spans
            .replace(/\x1b\[([0-9;]*)m/g, (match, codes) => {
                if (!codes || codes === '0') return '</span>'; // reset/close
                const styles = codes.split(';').map(code => ansiColors[code] || '').filter(Boolean);
                return styles.length > 0 ? `<span style="${styles.join('; ')}">` : '';
            })
            // Remove any other ANSI escape sequences that might interfere
            .replace(/\x1b\[[0-9;]*[a-zA-Z]/g, '')
            // Handle any unclosed spans at the end
            .replace(/(<span[^>]*>)(?!.*<\/span>)/g, '$1</span>');
    };

    const loadLogs = async (isManualRefresh = false) => {
        // Don't show loading state on auto-refresh unless it's manual
        if (isManualRefresh) {
            refreshLogsBtn.disabled = true;
            const icon = refreshLogsBtn.querySelector('i');
            if (icon) icon.classList.add('spin'); // Add spin class
        }
        try {
            const res = await fetch('/api/logs');
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
            const data = await res.json();

            // Process logs more carefully to preserve ANSI sequences and emojis
            const rawLogs = data.logs || '';

            const cleaned = rawLogs
                .split('\n')
                .map(line => {
                    // Only remove the timestamp, preserve everything else
                    line = line.replace(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?\s*/, '');
                    return line;
                })
                .filter(line => line.length > 0)
                .join('\n');

            // Convert ANSI sequences to HTML and update content
            const htmlContent = ansiToHtml(cleaned);

            // Only update if content has changed to avoid unnecessary reflows
            if (logsContent.innerHTML !== htmlContent) {
                 logsContent.innerHTML = htmlContent || "No logs found or logs are empty.";
                 // Scroll to bottom only if user is already near the bottom (optional heuristic)
                 // const isScrolledToBottom = logsContent.scrollHeight - logsContent.clientHeight <= logsContent.scrollTop + 10;
                 // if (isScrolledToBottom) {
                 //     logsContent.scrollTop = logsContent.scrollHeight;
                 // }
            }
        } catch (error) {
            // Only show error in console for auto-refresh, show in UI for manual
            if (isManualRefresh) {
                 logsContent.textContent = `Error loading logs: ${error.message}`;
            }
            console.error('Logs error:', error);
        } finally {
             if (isManualRefresh) {
                 refreshLogsBtn.disabled = false;
                 const icon = refreshLogsBtn.querySelector('i');
                 if (icon) icon.classList.remove('spin'); // Remove spin class
             }
        }
    };

    const stopLogPolling = () => {
        if (logInterval) {
            clearInterval(logInterval);
            logInterval = null;
            console.log("Log polling stopped.");
        }
    }

    const startLogPolling = () => {
         stopLogPolling(); // Clear any existing interval first
         if (logsTab.classList.contains('active') && document.visibilityState === 'visible') {
             console.log("Starting log polling...");
             loadLogs(); // Load immediately
             logInterval = setInterval(() => loadLogs(false), 15000); // Poll every 15 seconds
         } else {
             console.log("Log polling not started (tab not active or page hidden).");
         }
    }


    refreshLogsBtn.addEventListener('click', () => loadLogs(true)); // Pass true for manual refresh

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopLogPolling();
        } else {
             startLogPolling(); // Will only start if logs tab is active
        }
    });


    /* Environment Variables Handling */
    const loadEnv = async () => {
        // Indicate loading state
        envGrid.innerHTML = `<p style="grid-column: 1 / -1; padding: 1rem 0; color: var(--text-muted-color);">Loading variables...</p>`; // Span full width
        try {
            const res = await fetch('/api/env');
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
            const data = await res.json();
            renderEnvVars(data.env);
        } catch (error) {
            showNotification(`Failed to load environment variables: ${error.message}`, 'error');
            envGrid.innerHTML = `<p style="grid-column: 1 / -1; color: var(--danger-color); padding: 1rem 0;">Failed to load variables. Please try refreshing.</p>`; // Span full width
            envSection.dataset.loaded = 'false'; // Allow reloading on next tab visit
        }
    };

    const renderEnvVars = (envVars = []) => {
        envVars.sort((a, b) => a.key.localeCompare(b.key));

        envGrid.innerHTML = envVars.length > 0 ? envVars.map((env, index) => `
            <input type="text" class="input-field env-key" value="${env.key || ''}" placeholder="Key" data-index="${index}" aria-label="Environment variable key ${index + 1}">
            <input type="text" class="input-field env-value" value="${env.value || ''}" placeholder="Value" data-index="${index}" aria-label="Environment variable value ${index + 1}">
        `).join('') : `<p style="grid-column: 1 / -1; padding: 1rem 0; color: var(--text-muted-color);">No environment variables defined. Click 'Add Variable' to start.</p>`; // Span full width
        // Ensure grid is visible and bulk is hidden when rendering grid
         envGrid.style.display = 'grid';
         bulkEditArea.style.display = 'none';
         addVariableBtn.style.display = 'inline-flex';
    };

    const addNewVariable = () => {
        const placeholder = envGrid.querySelector('p');
        if (placeholder) placeholder.remove();

        const index = envGrid.querySelectorAll('.env-key').length;
        const keyInput = document.createElement('input');
        keyInput.type = 'text';
        keyInput.className = 'input-field env-key';
        keyInput.placeholder = 'Key';
        keyInput.dataset.index = index;
        keyInput.setAttribute('aria-label', `New environment variable key ${index + 1}`);

        const valueInput = document.createElement('input');
        valueInput.type = 'text';
        valueInput.className = 'input-field env-value';
        valueInput.placeholder = 'Value';
        valueInput.dataset.index = index;
        valueInput.setAttribute('aria-label', `New environment variable value ${index + 1}`);

        // Check if the grid is in single-column mode (mobile)
        const isSingleColumn = window.getComputedStyle(envGrid).gridTemplateColumns.split(' ').length === 1;

        if (isSingleColumn) {
             // Append key and value separately in single column mode
             envGrid.appendChild(keyInput);
             envGrid.appendChild(valueInput);
        } else {
             // Append both to let the grid place them
             envGrid.append(keyInput, valueInput);
        }

        keyInput.focus();
    };


    addVariableBtn.addEventListener('click', addNewVariable);

    // Toggle Bulk Edit mode
    bulkEditToggle.addEventListener('change', function () {
        if (this.checked) {
            const envVarsText = Array.from(envGrid.querySelectorAll('.env-key'))
                .map(keyInput => {
                    // Find the corresponding value input reliably
                    const index = keyInput.dataset.index;
                    const valueInput = envGrid.querySelector(`.env-value[data-index="${index}"]`);
                    const key = keyInput.value.trim();
                    const value = valueInput ? valueInput.value.trim() : '';
                    return (key) ? `${key}=${value}` : null;
                })
                 .filter(line => line !== null)
                 .sort()
                 .join('\n');
            bulkEditArea.value = envVarsText;
            envGrid.style.display = 'none';
            addVariableBtn.style.display = 'none';
            bulkEditArea.style.display = 'block';
            bulkEditArea.focus();
        } else {
            // Parse bulk area and render back to grid
            const envVars = bulkEditArea.value.split('\n')
                .map(line => line.trim())
                .filter(line => line && line.includes('='))
                .map(line => {
                    const separatorIndex = line.indexOf('=');
                    const key = line.substring(0, separatorIndex).trim();
                    const value = line.substring(separatorIndex + 1).trim();
                    return { key, value };
                })
                 .filter(env => env.key);

            renderEnvVars(envVars); // This function now handles showing grid/hiding bulk
        }
    });

    /* Save Environment Variables */
    const saveEnvChanges = async () => {
        saveEnvBtn.disabled = true;
        const originalHTML = saveEnvBtn.innerHTML; // Store original button content
        saveEnvBtn.innerHTML = `<i data-lucide="loader-2" class="spin"></i> Saving...`;
        lucide.createIcons({ icons: { 'loader-2': saveEnvBtn.querySelector('.spin') } });

        try {
            let envVars = [];
            if (bulkEditToggle.checked) {
                envVars = bulkEditArea.value.split('\n')
                    .map(line => line.trim())
                    .filter(line => line && line.includes('='))
                    .map(line => {
                         const separatorIndex = line.indexOf('=');
                         const key = line.substring(0, separatorIndex).trim();
                         const value = line.substring(separatorIndex + 1).trim();
                        return { key, value };
                    })
                     .filter(env => env.key);
            } else {
                const keyInputs = envGrid.querySelectorAll('.env-key');
                keyInputs.forEach(keyInput => {
                     // Find the corresponding value input reliably
                     const index = keyInput.dataset.index;
                     const valueInput = envGrid.querySelector(`.env-value[data-index="${index}"]`);
                    const key = keyInput.value.trim();
                    const value = valueInput ? valueInput.value.trim() : '';
                    if (key) {
                        envVars.push({ key, value });
                    }
                });
            }

            const res = await fetch('/api/env', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({env: envVars})
            });

            if (!res.ok) {
                 const errorData = await res.json().catch(() => ({ error: 'Failed to parse error response' }));
                 throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            if (data.success) {
                showNotification('Environment variables saved successfully');
                // Re-render the grid view to ensure consistency and sorting, even if saving from bulk
                 renderEnvVars(envVars);
                 // Uncheck toggle if save was successful in bulk mode
                 if (bulkEditToggle.checked) {
                      bulkEditToggle.checked = false;
                      // RenderEnvVars already handles switching view
                 }

            } else {
                throw new Error(data.error || 'Failed to save environment variables');
            }
        } catch (error) {
            showNotification(`Error saving: ${error.message}`, 'error');
            console.error('Save ENV error:', error);
        } finally {
            saveEnvBtn.disabled = false;
            saveEnvBtn.innerHTML = originalHTML; // Restore original content
            lucide.createIcons(); // Re-render all icons
        }
    };

    saveEnvBtn.addEventListener('click', saveEnvChanges);

    /* Handle Restart & Rebuild Actions */
    const handleAction = async (action, button) => {
         button.disabled = true;
         const originalHTML = button.innerHTML;
         button.innerHTML = `<i data-lucide="loader-2" class="spin"></i> ${action.charAt(0).toUpperCase() + action.slice(1)}ing...`;
         lucide.createIcons({ icons: { 'loader-2': button.querySelector('.spin') } });

        try {
            const res = await fetch(`/api/${action}`, {method: 'POST'});

            if (!res.ok) {
                 const errorData = await res.json().catch(() => ({ error: 'Failed to parse error response' }));
                 throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            if (data.success) {
                showNotification(`${action.charAt(0).toUpperCase() + action.slice(1)} initiated successfully.`);
                 switchTab(logsTab); // Switch to logs tab
                 setTimeout(() => loadLogs(true), 500); // Refresh logs manually after a short delay
            } else {
                throw new Error(data.error || `${action} failed`);
            }
        } catch (error) {
            showNotification(`Error during ${action}: ${error.message}`, 'error');
            console.error(`${action} error:`, error);
        } finally {
             button.disabled = false;
             button.innerHTML = originalHTML; // Restore original content
             lucide.createIcons();
        }
    };

    restartBtn.addEventListener('click', () => handleAction('restart', restartBtn));
    rebuildBtn.addEventListener('click', () => handleAction('rebuild', rebuildBtn));

    // Initial load for the default tab and start polling
    startLogPolling();

</script>
</body>
</html>